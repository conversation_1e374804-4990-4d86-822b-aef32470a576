import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class UserProfile {
  final String name;
  final int age;
  final String location;
  final String countryCode;
  final List<String> focusAreas;
  final DateTime createdAt;
  final DateTime lastUpdated;

  UserProfile({
    required this.name,
    required this.age,
    required this.location,
    required this.countryCode,
    required this.focusAreas,
    required this.createdAt,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() => {
    'name': name,
    'age': age,
    'location': location,
    'countryCode': countryCode,
    'focusAreas': focusAreas,
    'createdAt': createdAt.millisecondsSinceEpoch,
    'lastUpdated': lastUpdated.millisecondsSinceEpoch,
  };

  factory UserProfile.fromJson(Map<String, dynamic> json) => UserProfile(
    name: json['name'],
    age: json['age'],
    location: json['location'],
    countryCode: json['countryCode'],
    focusAreas: List<String>.from(json['focusAreas']),
    createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
    lastUpdated: DateTime.fromMillisecondsSinceEpoch(json['lastUpdated']),
  );

  UserProfile copyWith({
    String? name,
    int? age,
    String? location,
    String? countryCode,
    List<String>? focusAreas,
    DateTime? createdAt,
    DateTime? lastUpdated,
  }) {
    return UserProfile(
      name: name ?? this.name,
      age: age ?? this.age,
      location: location ?? this.location,
      countryCode: countryCode ?? this.countryCode,
      focusAreas: focusAreas ?? this.focusAreas,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }
}

class UserProfileService {
  static const String _profileKey = 'user_profile_v1';
  static const String _onboardingCompleteKey = 'onboarding_complete';

  /// Create or update user profile
  static Future<UserProfile> saveProfile({
    required String name,
    required int age,
    required String location,
    required String countryCode,
    required List<String> focusAreas,
  }) async {
    final now = DateTime.now();
    final existingProfile = await getProfile();

    final profile = UserProfile(
      name: name,
      age: age,
      location: location,
      countryCode: countryCode,
      focusAreas: focusAreas,
      createdAt: existingProfile?.createdAt ?? now,
      lastUpdated: now,
    );

    await _saveProfile(profile);
    return profile;
  }

  /// Get current user profile
  static Future<UserProfile?> getProfile() async {
    final prefs = await SharedPreferences.getInstance();
    final profileJson = prefs.getString(_profileKey);

    if (profileJson == null) return null;

    try {
      final profileData = json.decode(profileJson);
      return UserProfile.fromJson(profileData);
    } catch (e) {
      return null;
    }
  }

  /// Update specific profile fields
  static Future<UserProfile?> updateProfile({
    String? name,
    int? age,
    String? location,
    String? countryCode,
    List<String>? focusAreas,
  }) async {
    final currentProfile = await getProfile();
    if (currentProfile == null) return null;

    final updatedProfile = currentProfile.copyWith(
      name: name,
      age: age,
      location: location,
      countryCode: countryCode,
      focusAreas: focusAreas,
    );

    await _saveProfile(updatedProfile);
    return updatedProfile;
  }

  /// Save profile to storage
  static Future<void> _saveProfile(UserProfile profile) async {
    final prefs = await SharedPreferences.getInstance();
    final profileJson = json.encode(profile.toJson());
    await prefs.setString(_profileKey, profileJson);
  }

  /// Check if user has completed onboarding
  static Future<bool> hasCompletedOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_onboardingCompleteKey) ?? false;
  }

  /// Mark onboarding as complete
  static Future<void> completeOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_onboardingCompleteKey, true);
  }

  /// Clear user profile (for logout/reset)
  static Future<void> clearProfile() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_profileKey);
    await prefs.remove(_onboardingCompleteKey);
  }

  /// Delete all user data (for account deletion)
  static Future<void> deleteAllUserData() async {
    final prefs = await SharedPreferences.getInstance();

    // Clear profile data
    await prefs.remove(_profileKey);
    await prefs.remove(_onboardingCompleteKey);

    // Clear journal entries
    await prefs.remove('journal_entries_v1');

    // Clear session data
    await prefs.remove('counselor_sessions_v1');

    // Clear safety incidents
    await prefs.remove('safety_incidents_v1');

    // Clear mood tracking data
    final keys = prefs.getKeys();
    final moodKeys =
        keys
            .where(
              (key) =>
                  key.contains('_morning') ||
                  key.contains('_afternoon') ||
                  key.contains('_evening') ||
                  key.contains('_value'),
            )
            .toList();

    for (final key in moodKeys) {
      await prefs.remove(key);
    }

    // Clear any other app preferences that might contain user data
    final userDataKeys =
        keys
            .where(
              (key) =>
                  key.startsWith('user_') ||
                  key.startsWith('mood_') ||
                  key.startsWith('session_') ||
                  key.startsWith('journal_'),
            )
            .toList();

    for (final key in userDataKeys) {
      await prefs.remove(key);
    }
  }

  /// Get user's display name with fallback
  static Future<String> getDisplayName() async {
    final profile = await getProfile();
    return profile?.name ?? 'User';
  }

  /// Get user's location for services
  static Future<String?> getUserLocation() async {
    final profile = await getProfile();
    return profile?.location;
  }

  /// Get user's country code for emergency services
  static Future<String?> getCountryCode() async {
    final profile = await getProfile();
    return profile?.countryCode;
  }

  /// Get user's focus areas
  static Future<List<String>> getFocusAreas() async {
    final profile = await getProfile();
    return profile?.focusAreas ?? [];
  }

  /// Check if profile is complete
  static Future<bool> isProfileComplete() async {
    final profile = await getProfile();
    return profile != null &&
        profile.name.isNotEmpty &&
        profile.age > 0 &&
        profile.location.isNotEmpty &&
        profile.countryCode.isNotEmpty &&
        profile.focusAreas.isNotEmpty;
  }
}
