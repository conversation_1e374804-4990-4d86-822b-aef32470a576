import 'package:flutter/material.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Policy'),
        backgroundColor: colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              colorScheme.primary.withValues(alpha: 0.03),
              colorScheme.surface,
            ],
          ),
        ),
        child: Safe<PERSON>rea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: colorScheme.surface,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: colorScheme.primary.withValues(alpha: 0.2)),
                    boxShadow: [
                      BoxShadow(
                        color: colorScheme.primary.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.privacy_tip_outlined, color: colorScheme.primary, size: 24),
                          const SizedBox(width: 12),
                          Text(
                            'Privacy Policy',
                            style: theme.textTheme.titleLarge?.copyWith(
                              color: colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Last updated: ${DateTime.now().toString().split(' ')[0]}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                _buildSection(
                  context,
                  'Information We Collect',
                  '''We collect information you provide directly to us, such as:

• Personal information during account setup (name, age, location)
• Focus areas and wellness preferences
• Journal entries and mood tracking data
• Session interactions with AI counselors
• Feedback and support communications

All data is stored locally on your device and is not transmitted to external servers unless explicitly stated.''',
                ),

                const SizedBox(height: 16),

                _buildSection(
                  context,
                  'How We Use Your Information',
                  '''We use your information to:

• Provide personalized mental health support and resources
• Improve AI counselor interactions and recommendations
• Track your wellness progress and mood patterns
• Customize your app experience based on your focus areas
• Provide crisis intervention when safety concerns are detected

Your data is used solely to enhance your mental health journey and is never sold or shared with third parties.''',
                ),

                const SizedBox(height: 16),

                _buildSection(
                  context,
                  'Data Storage and Security',
                  '''Your privacy and security are our top priorities:

• All personal data is encrypted and stored locally on your device
• We use industry-standard security measures to protect your information
• Session data with AI counselors is processed securely and not stored permanently
• You have full control over your data and can delete it at any time
• We do not access your data without your explicit consent''',
                ),

                const SizedBox(height: 16),

                _buildSection(
                  context,
                  'Crisis Intervention',
                  '''For your safety, we may override privacy settings in emergency situations:

• If our AI detects immediate risk of self-harm or suicide
• When you explicitly request emergency assistance
• To provide appropriate crisis resources and hotline information
• This is done solely to ensure your safety and wellbeing

Emergency interventions are logged for safety purposes but are kept confidential.''',
                ),

                const SizedBox(height: 16),

                _buildSection(
                  context,
                  'Your Rights',
                  '''You have the right to:

• Access all data we have about you
• Correct or update your personal information
• Delete your account and all associated data
• Export your data in a readable format
• Opt out of data collection features
• Contact us with privacy concerns or questions

To exercise these rights, use the profile settings or contact our support team.''',
                ),

                const SizedBox(height: 16),

                _buildSection(
                  context,
                  'Contact Us',
                  '''If you have questions about this Privacy Policy or our data practices, please contact us:

Email: <EMAIL>
Support: <EMAIL>

We are committed to protecting your privacy and will respond to all inquiries within 48 hours.''',
                ),

                const SizedBox(height: 32),

                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: colorScheme.primaryContainer.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: colorScheme.primary.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: colorScheme.primary,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'This privacy policy may be updated periodically. We will notify you of any significant changes through the app.',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
