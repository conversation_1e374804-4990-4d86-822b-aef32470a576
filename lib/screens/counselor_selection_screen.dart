import 'package:flutter/material.dart';
import 'package:stillpoint/services/session_service.dart';
import 'package:stillpoint/screens/chat_screen.dart';

class CounselorSelectionScreen extends StatefulWidget {
  final String userName;

  const CounselorSelectionScreen({super.key, required this.userName});

  @override
  State<CounselorSelectionScreen> createState() =>
      _CounselorSelectionScreenState();
}

class _CounselorSelectionScreenState extends State<CounselorSelectionScreen> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;

  final List<Map<String, dynamic>> counselors = [
    {
      'name': 'Dr. <PERSON>',
      'title': 'The Wise Mentor',
      'color': const Color(0xFF4CAF50), // Green
      'approach': 'Mindfulness, CBT, and stoic philosophy',
      'idealFor': 'Clarity, emotional regulation, and personal growth',
      'quote': '"Pause. Reflect. There\'s power in your stillness."',
      'tone': 'Gentle, thought-provoking, and grounded',
      'specialty': 'Anxiety, Burnout, Life Transitions, Overthinking',
      'bio':
          'Dr. <PERSON> helps you slow down and navigate challenges with clarity. Using mindfulness and stoic philosophy, He\'ll guide you to find wisdom in struggles and peace in progress.',
      'image': '🧘‍♂️',
      'imagePath': 'assets/images/counselors/drsage.png',
    },
    {
      'name': 'Luna',
      'title': 'The Compassionate Guide',
      'color': const Color(0xFFB39DDB), // Lilac
      'approach': 'Trauma-informed care, inner child work, gentle prompts',
      'idealFor': 'Healing from emotional wounds or seeking self-love',
      'quote': '"You\'re not broken. You\'re becoming."',
      'tone': 'Warm, nurturing, deeply empathetic',
      'specialty': 'Trauma, Self-Esteem, Relationships, Emotional Healing',
      'bio':
          'Luna offers a safe space to feel deeply and heal at your own pace. With soothing words and nurturing presence, she believes in the power of self-compassion.',
      'image': '🌙',
      'imagePath': 'assets/images/counselors/luna.png',
    },
    {
      'name': 'Kai',
      'title': 'The Direct Motivator',
      'color': const Color(0xFFFF9800), // Orange
      'approach': 'Action-oriented, goal setting, mindset coaching',
      'idealFor': 'Those wanting accountability and structured guidance',
      'quote': '"Let\'s stop talking about it and take the first step."',
      'tone': 'Honest, encouraging, sometimes tough-love',
      'specialty': 'Productivity, Career, ADHD, Breaking Patterns',
      'bio':
          'Kai provides practical advice and accountability when you need it most. His firm but supportive style helps you build momentum and take action.',
      'image': '⚡',
      'imagePath': 'assets/images/counselors/Kai.png',
    },
    {
      'name': 'Theo',
      'title': 'The Rational Listener',
      'color': const Color(0xFF2196F3), // Blue
      'approach': 'Logical reflection, emotional detachment for clarity',
      'idealFor': 'Making sense of emotions logically',
      'quote': '"Let\'s understand the why before the what."',
      'tone': 'Neutral, focused, analytical',
      'specialty': 'Decision-Making, Conflict Resolution, Overthinking',
      'bio':
          'Theo brings clarity to emotional chaos with logical, unbiased guidance. He asks the right questions to help you solve problems and gain fresh perspective.',
      'image': '🤔',
      'imagePath': 'assets/images/counselors/theo.png',
    },
    {
      'name': 'Dr. Elena',
      'title': 'The Science-Based Whisperer',
      'color': const Color(0xFF2E7D32), // Deep green
      'approach': 'Evidence-based therapy, CBT, neuroscience-backed tools',
      'idealFor': 'Therapy rooted in results',
      'quote': '"You\'re not stuck—you just haven\'t learned the method yet."',
      'tone': 'Clear, methodical, supportive without fluff',
      'specialty':
          'Anxiety, Cognitive Distortions, Habit Change, Emotional Regulation',
      'bio':
          'Dr. Elena provides science-based therapy that makes sense. She guides you with CBT techniques and brain-based insights to build new thinking patterns.',
      'image': '🧠',
      'imagePath': 'assets/images/counselors/drelena.png',
    },
    {
      'name': 'Zuri',
      'title': 'The Grounded Ally',
      'color': const Color(0xFFE91E63), // Pink
      'approach':
          'Identity-affirming therapy, trauma-informed, intersectional lens',
      'idealFor': 'Navigating identity and cultural healing',
      'quote':
          '"You don\'t have to shrink to be safe. Your story belongs here."',
      'tone': 'Grounded, ompassionate, socially aware',
      'specialty':
          'Identity & Belonging, Cultural Trauma, Racial Stress, Self-Worth, Intergenerational Healing',
      'bio':
          'Zuri understands that healing isn\'t one-size-fits-all. They create space for your full self to exist and help you reclaim your voice with cultural awareness.',
      'image': '✊🏾',
      'imagePath': 'assets/images/counselors/zuri.png',
    },
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _selectCounselor(Map<String, dynamic> counselor) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(counselor['color']),
                ),
                const SizedBox(height: 16),
                Text('Starting session with ${counselor['name']}...'),
              ],
            ),
          ),
    );

    try {
      // Create new session
      final session = await SessionService.createSession(
        counselorName: counselor['name'],
        counselorImage: counselor['image'],
        counselorImagePath: counselor['imagePath'],
        counselorColor: counselor['color'].value,
      );

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Navigate to chat screen
      if (mounted) {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => ChatScreen(session: session)),
        );

        // If chat screen returned true, pop back to counselor screen to refresh
        if (result == true && mounted) {
          Navigator.pop(context, true);
        }
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Show error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start session: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              colorScheme.primary.withValues(alpha: 0.05),
              colorScheme.surface,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 20),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: colorScheme.surface,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: Icon(
                              Icons.arrow_back_ios_new,
                              color: colorScheme.primary,
                              size: 20,
                            ),
                          ),
                        ),
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                'Choose Your Counselor',
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  color: colorScheme.primary,
                                  fontWeight: FontWeight.w800,
                                  letterSpacing: -0.5,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Find your perfect therapeutic match',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: colorScheme.onSurfaceVariant,
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 56), // Balance the back button
                      ],
                    ),
                  ],
                ),
              ),

              // Modern page indicator
              Container(
                height: 6,
                margin: const EdgeInsets.symmetric(horizontal: 32),
                child: Row(
                  children: List.generate(
                    counselors.length,
                    (index) => Expanded(
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                        margin: const EdgeInsets.symmetric(horizontal: 3),
                        decoration: BoxDecoration(
                          color:
                              index == _currentIndex
                                  ? counselors[_currentIndex]['color']
                                  : colorScheme.outline.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(3),
                          boxShadow:
                              index == _currentIndex
                                  ? [
                                    BoxShadow(
                                      color: counselors[_currentIndex]['color']
                                          .withValues(alpha: 0.4),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ]
                                  : null,
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Counselor cards
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentIndex = index;
                    });
                  },
                  itemCount: counselors.length,
                  itemBuilder: (context, index) {
                    final counselor = counselors[index];
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: _buildCounselorCard(counselor, theme),
                    );
                  },
                ),
              ),

              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCounselorCard(Map<String, dynamic> counselor, ThemeData theme) {
    final colorScheme = theme.colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(28),
        border: Border.all(
          color: counselor['color'].withValues(alpha: 0.1),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: counselor['color'].withValues(alpha: 0.08),
            blurRadius: 24,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 40,
            offset: const Offset(0, 16),
          ),
        ],
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile section
            Column(
              children: [
                // Profile image with animated border
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border: Border.all(
                      color: counselor['color'].withValues(alpha: 0.4),
                      width: 3,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: counselor['color'].withValues(alpha: 0.2),
                        blurRadius: 16,
                        offset: const Offset(0, 6),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(47),
                    child: _buildCounselorImage(counselor),
                  ),
                ),
                const SizedBox(height: 12),

                // Name and title
                Text(
                  counselor['name'],
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.w800,
                    fontSize: 22,
                    letterSpacing: -0.5,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  counselor['title'],
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: counselor['color'],
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 18),

            // Quote
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    counselor['color'].withValues(alpha: 0.08),
                    counselor['color'].withValues(alpha: 0.04),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: counselor['color'].withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.format_quote, color: counselor['color'], size: 20),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      counselor['quote'],
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface,
                        fontStyle: FontStyle.italic,
                        fontWeight: FontWeight.w600,
                        height: 1.4,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Specialty Areas with bubbles
            _buildSpecialtySection(counselor, theme, colorScheme),

            const SizedBox(height: 16),

            // Approach
            _buildInfoSection(
              'Approach',
              counselor['approach'],
              counselor['color'],
              theme,
              Icons.lightbulb_outline,
            ),

            const SizedBox(height: 16),

            // Bio
            _buildInfoSection(
              'About ${counselor['name']}',
              counselor['bio'],
              counselor['color'],
              theme,
              Icons.person_outline,
            ),

            const SizedBox(height: 20),

            // Start session button
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    counselor['color'],
                    counselor['color'].withValues(alpha: 0.8),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: counselor['color'].withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: () => _selectCounselor(counselor),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: Colors.white,
                  shadowColor: Colors.transparent,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.chat_bubble_outline, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Start Session with ${counselor['name']}',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecialtySection(
    Map<String, dynamic> counselor,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    // Split specialty areas by comma and create bubbles
    final List<String> specialties = counselor['specialty'].split(', ');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.psychology_outlined,
              color: counselor['color'],
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Specialty Areas',
              style: theme.textTheme.titleSmall?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _buildSpecialtyBubbles(specialties, counselor, theme),
        ),
      ],
    );
  }

  List<Widget> _buildSpecialtyBubbles(
    List<String> specialties,
    Map<String, dynamic> counselor,
    ThemeData theme,
  ) {
    return specialties.asMap().entries.map((entry) {
      final index = entry.key;
      final specialty = entry.value;

      return AnimatedContainer(
        duration: Duration(milliseconds: 200 + (index * 50)),
        curve: Curves.easeOutBack,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              counselor['color'].withValues(alpha: 0.15),
              counselor['color'].withValues(alpha: 0.08),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: counselor['color'].withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: counselor['color'].withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          specialty.trim(),
          style: theme.textTheme.bodySmall?.copyWith(
            color: counselor['color'],
            fontWeight: FontWeight.w700,
            fontSize: 12,
          ),
        ),
      );
    }).toList();
  }

  Widget _buildInfoSection(
    String title,
    String content,
    Color color,
    ThemeData theme,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 16),
              ),
              const SizedBox(width: 10),
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w700,
                  fontSize: 13,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface,
              height: 1.4,
              fontSize: 13,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCounselorImage(Map<String, dynamic> counselor) {
    return Image.asset(
      counselor['imagePath'],
      width: 100,
      height: 100,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        // Fallback to emoji if image fails to load
        return Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                counselor['color'].withValues(alpha: 0.2),
                counselor['color'].withValues(alpha: 0.1),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(50),
          ),
          child: Center(
            child: Text(
              counselor['image'],
              style: const TextStyle(fontSize: 40),
            ),
          ),
        );
      },
    );
  }
}
